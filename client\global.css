@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /**
   * Tailwind CSS theme
   * tailwind.config.ts expects the following color variables to be expressed as HSL values.
   * A different format will require also updating the theme in tailwind.config.ts.
  */
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Custom animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
}

.animate-slide-up {
  animation: slide-up 1s ease-out forwards;
  animation-delay: 0.3s;
  opacity: 0;
}

/* Mobile Performance Optimizations */
@media (max-width: 768px) {
  /* Reduce animation complexity on mobile for better performance */
  * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Optimize touch interactions */
  button, [role="button"], input[type="submit"] {
    touch-action: manipulation;
  }
  
  /* Prevent horizontal scrolling on mobile */
  html, body {
    overflow-x: hidden;
  }
}

/* Reduced Motion Support for Accessibility */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .animate-fade-in,
  .animate-slide-up,
  .animate-float-slow,
  .animate-float-mole {
    animation: none !important;
  }
}

/* Design System Custom Properties */
:root {
  /* Animation durations */
  --animation-duration-fast: 150ms;
  --animation-duration-normal: 300ms;
  --animation-duration-slow: 500ms;
  --animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Ethos color palette */
  --ethos-purple: #7409C5;
  --ethos-purple-light: #8235F4;
  --ethos-purple-gradient-start: #7471E0;
  --ethos-purple-gradient-end: #EA73D4;
  --ethos-navy: #030823;
  --ethos-navy-light: #0D1144;
  --ethos-gray: #484848;
  --ethos-gray-light: #717493;
  --ethos-gray-lighter: #9E9898;
  
  /* Section backgrounds */
  --bg-gray-section: #F2F2F2;
  --bg-gradient-ways: linear-gradient(180deg, #FFF 37.11%, #E8E8E8 100%);
  --bg-gradient-testimonials: linear-gradient(180deg, #FFF 60.69%, #DDD 100%);
  
  /* Shadow variations */
  --shadow-purple: 0 10px 15px rgba(116, 113, 224, 0.1);
  --shadow-neumorphic: 0 4px 4px rgba(0, 0, 0, 0.25), inset -30px -30px 50px rgba(255, 255, 255, 0.7), inset 30px 30px 50px rgba(0, 39, 80, 0.05);
  --shadow-glass: 0 8px 32px rgba(116, 113, 224, 0.08), 0 4px 16px rgba(0, 0, 0, 0.04), inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* Floating animations */
@keyframes float {
  0% { transform: translate(-50%, -50%) translateY(0); }
  50% { transform: translate(-50%, -50%) translateY(-1mm); }
  100% { transform: translate(-50%, -50%) translateY(0); }
}

@keyframes floatMole {
  0% { transform: translateY(0); }
  50% { transform: translateY(-1mm); }
  100% { transform: translateY(0); }
}

.animate-float-slow {
  animation: float 4s ease-in-out infinite;
}

.animate-float-mole {
  animation: floatMole 4s ease-in-out infinite;
}

/* Hardware acceleration utilities */
.gpu-accelerated {
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  will-change: transform;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .animate-float-slow,
  .animate-float-mole {
    animation: none;
  }
}

/* Design system utility classes */
.bg-ethos-purple {
  background-color: var(--ethos-purple);
}

.bg-ethos-purple-light {
  background-color: var(--ethos-purple-light);
}

.text-ethos-purple {
  color: var(--ethos-purple);
}

.text-ethos-navy {
  color: var(--ethos-navy);
}

.text-ethos-gray {
  color: var(--ethos-gray);
}

.shadow-purple {
  box-shadow: var(--shadow-purple);
}

.shadow-neumorphic {
  box-shadow: var(--shadow-neumorphic);
}

.shadow-glass {
  box-shadow: var(--shadow-glass);
}

/* Progressive enhancement utilities */
.progressive-text-scale {
  @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl;
}

.progressive-padding {
  @apply px-4 sm:px-6 lg:px-8;
}

.progressive-section-padding {
  @apply py-16 md:py-20 lg:py-24 xl:py-28;
}

.standard-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.card-border-radius {
  @apply rounded-[40px] sm:rounded-[50px] lg:rounded-[57px];
}

.card-padding {
  @apply p-4 sm:p-6 lg:p-8 xl:p-10;
}

/* Focus management for accessibility */
.focus-ring {
  @apply focus:outline-none focus-visible:ring-2 focus-visible:ring-ethos-purple focus-visible:ring-offset-2;
}

/* Interaction states */
.hover-lift {
  @apply hover:scale-[1.02] transition-transform duration-300;
}

.hover-shadow {
  @apply hover:shadow-xl transition-shadow duration-300;
}

/* Grid utilities */
.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}
