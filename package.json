{"name": "fusion-starter", "private": true, "type": "module", "homepage": "https://uththunga.github.io/ethos-prompt-UI/", "pkg": {"assets": ["dist/spa/*"], "scripts": ["dist/server/**/*.js"]}, "scripts": {"dev": "vite", "build": "npm run build:client && npm run build:server", "build:client": "vite build", "build:server": "vite build --config vite.config.server.ts", "start": "node dist/server/node-build.mjs", "test": "vitest --run", "format.fix": "prettier --write .", "typecheck": "tsc", "predeploy": "npm run build:client", "deploy": "gh-pages -d dist/spa"}, "dependencies": {"dotenv": "^17.2.0", "embla-carousel-autoplay": "^8.6.0", "express": "^4.18.2", "framer-motion": "^12.23.12", "i18next": "^25.4.0", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "jszip": "^3.10.1", "lottie-react": "^2.4.1", "next": "^15.5.2", "ogl": "^1.0.11", "react-i18next": "^15.7.1", "react-icons": "^5.5.0", "zod": "^3.23.8"}, "devDependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@react-three/drei": "^10.1.2", "@react-three/fiber": "^8.18.0", "@swc/core": "^1.11.24", "@tailwindcss/typography": "^0.5.15", "@tanstack/react-query": "^5.56.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "gh-pages": "^6.3.0", "globals": "^15.9.0", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "postcss": "^8.5.6", "prettier": "^3.5.3", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "serverless-http": "^3.2.0", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss": "^3.4.11", "tailwindcss-animate": "^1.0.7", "tsx": "^4.7.0", "typescript": "^5.5.3", "vaul": "^0.9.3", "vite": "^6.2.2", "vitest": "^3.1.4"}}